// This file is needed for development and testing purposes only
// It will not be included in the final package

import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import * as bodyParser from 'body-parser';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // for Stripe webhook route
  app.use('/stripe/webhooks', bodyParser.raw({ type: 'application/json' }));

  // Enable CORS for all origins
  app.enableCors({
    origin: '*', // allow all origins
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE',
    credentials: true,
  });

  await app.listen(3000);
  console.log('Stripe module development server running on port 3000');
}

// Only bootstrap if this file is run directly, not when imported as a library
if (require.main === module) {
  // Use void to explicitly mark the promise as handled
  void bootstrap();
}
