# @sparrowapp-dev/stripe-billing

A reusable Stripe billing module for NestJS applications that provides easy integration with Stripe payment services for handling payment methods, customers, and setup intents.

## Features

- Create and manage Stripe customers
- Add payment methods (credit cards) using Stripe Elements
- Update billing details for saved payment methods
- List all payment methods for a customer
- Delete payment methods
- Secure API endpoints with proper validation
- Easy configuration through environment variables or direct options

## Installation

```bash
# Configure GitHub Packages in .npmrc
@sparrowapp-dev:registry=https://npm.pkg.github.com/
//npm.pkg.github.com/:_authToken=${GITHUB_TOKEN}

# Install the package
npm install @sparrowapp-dev/stripe-billing
```

You can also use the setup script provided with the package:

```bash
# Run the setup script for quick configuration
npx @sparrowapp-dev/stripe-billing/scripts/setup-consumer.js
```

## Configuration

### Environment Variables

Create a `.env` file with your Stripe API keys:

```
STRIPE_SECRET_KEY=sk_test_...
STRIPE_PUBLISHABLE_KEY=pk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...
```

### Module Registration

```typescript
import { Module } from '@nestjs/common';
import { StripeModule } from '@sparrowapp-dev/stripe-billing';

@Module({
  imports: [
    StripeModule.register({
      secretKey: process.env.STRIPE_SECRET_KEY,
      publishableKey: process.env.STRIPE_PUBLISHABLE_KEY,
      webhookSecret: process.env.STRIPE_WEBHOOK_SECRET,
      isGlobal: true, // optional, default is false
      registerControllers: true, // optional, default is true
    }),
  ],
})
export class AppModule {}
```

## API Reference

### Services

#### StripeService

```typescript
import { Injectable } from '@nestjs/common';
import { StripeService } from '@sparrowapp-dev/stripe-billing';

@Injectable()
export class PaymentService {
  constructor(private readonly stripeService: StripeService) {}

  // Get publishable key for frontend initialization
  getPublishableKey() {
    return this.stripeService.getPublicKey();
  }

  // Create a customer in Stripe
  async createCustomer(email: string, metadata?: Record<string, string>) {
    return this.stripeService.createCustomer(email, metadata);
  }

  // Retrieve a customer from Stripe
  async getCustomer(customerId: string) {
    return this.stripeService.getCustomer(customerId);
  }

  // Create a setup intent for securely collecting card details
  async createSetupIntent(customerId: string) {
    return this.stripeService.createSetupIntent(customerId);
  }

  // Access the Stripe instance directly for advanced operations
  getStripeInstance() {
    return this.stripeService.getStripeInstance();
  }
}
```

#### PaymentMethodsService

```typescript
import { Injectable } from '@nestjs/common';
import { PaymentMethodsService } from '@sparrowapp-dev/stripe-billing';

@Injectable()
export class CardService {
  constructor(private readonly paymentMethodsService: PaymentMethodsService) {}

  // Get all payment methods for a customer
  async getPaymentMethods(customerId: string) {
    return this.paymentMethodsService.getPaymentMethods(customerId);
  }

  // Get a specific payment method
  async getPaymentMethod(paymentMethodId: string) {
    return this.paymentMethodsService.getPaymentMethod(paymentMethodId);
  }

  // Update billing details for a payment method
  async updatePaymentMethodBillingDetails(
    paymentMethodId: string,
    billingDetails: any,
  ) {
    return this.paymentMethodsService.updatePaymentMethodBillingDetails(
      paymentMethodId,
      billingDetails,
    );
  }

  // Delete a payment method
  async detachPaymentMethod(paymentMethodId: string) {
    return this.paymentMethodsService.detachPaymentMethod(paymentMethodId);
  }
}
```

### REST API Endpoints

When controllers are registered (default behavior), the following endpoints are available:

#### Stripe Controller (`/stripe`)

- `GET /stripe/config` - Get Stripe publishable key
- `POST /stripe/customers` - Create a new Stripe customer
- `GET /stripe/customers/:id` - Get a Stripe customer by ID
- `POST /stripe/setup-intents` - Create a setup intent for securely collecting card details

#### Payment Methods Controller (`/payment-methods`)

- `GET /payment-methods/customer/:customerId` - Get all payment methods for a customer
- `GET /payment-methods/:id` - Get details of a specific payment method
- `PUT /payment-methods/:id/billing-details` - Update billing details for a payment method
- `DELETE /payment-methods/:id` - Delete (detach) a payment method

## Frontend Integration

To integrate with your frontend, follow these steps:

1. Get the publishable key from `/stripe/config`
2. Create a customer via `/stripe/customers`
3. Create a setup intent via `/stripe/setup-intents`
4. Use the client secret with Stripe Elements to securely collect card information
5. Once the card is added, manage it through the payment methods endpoints

Example frontend integration with Stripe Elements:

```javascript
// Initialize Stripe with publishable key
const stripe = Stripe('pk_test_...');

// Create elements instance
const elements = stripe.elements();

// Create card element
const card = elements.create('card');
card.mount('#card-element');

// Handle form submission
form.addEventListener('submit', async (event) => {
  event.preventDefault();

  // Get setup intent client secret from your backend
  const { clientSecret } = await fetchSetupIntent(customerId);

  // Confirm card setup
  const result = await stripe.confirmCardSetup(clientSecret, {
    payment_method: {
      card: card,
      billing_details: { name: 'Customer Name' },
    },
  });

  if (result.error) {
    // Handle error
  } else {
    // Card has been successfully set up
    // paymentMethodId = result.setupIntent.payment_method
  }
});
```

## Optional Dependencies

This package has the following optional dependencies:

- `@nestjs/swagger` - For API documentation
- `class-validator` and `class-transformer` - For DTO validation

## Development and Publishing

### Development

```bash
# Clone the repository
git clone https://github.com/sparrowapp-dev/sparrow-packages.git

# Navigate to the package directory
cd sparrow-packages/packages/@sparrow-billing/stripe

# Install dependencies
npm install

# Build the package
npm run build

# Run tests
npm run test
```

### Publishing

This package includes an automated publishing script that handles GitHub Packages authentication and package publishing in one step:

```bash
# Set your GitHub token as an environment variable
export GITHUB_TOKEN=your_github_token

# Run the publish script
npm run publish-package
```

The `publish.js` script will:

1. Create a temporary `.npmrc` file with your GitHub token
2. Clean and rebuild the package with proper TypeScript declarations
3. Publish the package to GitHub Packages
4. Clean up the temporary file

This makes it easy to publish new versions without manually managing authentication or build steps.

## Compatibility

- NestJS v10.x and v11.x
- Stripe.js v14.x

## License

Copyright (c) 2025 Techdome Solutions Private Limited India. All Rights Reserved.

This software and associated documentation files (the "Software") are the confidential and proprietary information of Techdome Solutions Private Limited India ("Techdome").

All title, ownership rights, and intellectual property rights in and to the Software remain vested exclusively in Techdome.

You shall not disclose, use, reproduce, modify, distribute, or transmit the Software or any part thereof in any form or by any means, electronic or mechanical, including photocopying, recording, or by any information storage and retrieval system, without the prior written permission of Techdome Solutions Private Limited India.

Access to and use of this repository and its contents are restricted to authorized personnel of Techdome Solutions Private Limited India for internal development and operational purposes only.

Any unauthorized use, reproduction, or distribution of the Software is strictly prohibited and may result in legal action.

For any inquiries regarding the use or licensing of this Software, please contact:

Techdome Solutions Private Limited India
<EMAIL>
