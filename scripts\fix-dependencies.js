#!/usr/bin/env node

/**
 * Dependency Fix Script for @sparrowapp-dev/stripe-billing
 * 
 * This script helps resolve npm dependency and lock file issues
 * that can occur during CI/CD pipeline execution.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔧 Fixing dependencies for @sparrowapp-dev/stripe-billing...\n');

// Configuration
const PACKAGE_PATH = 'packages/@sparrow-billing/stripe';

/**
 * Check if we're in the correct directory
 */
function validateEnvironment() {
  console.log('📍 Validating environment...');
  
  if (!fs.existsSync(PACKAGE_PATH)) {
    console.error(`❌ Package directory not found: ${PACKAGE_PATH}`);
    console.error('Please run this script from the repository root.');
    process.exit(1);
  }
  
  if (!fs.existsSync(path.join(PACKAGE_PATH, 'package.json'))) {
    console.error(`❌ package.json not found in ${PACKAGE_PATH}`);
    process.exit(1);
  }
  
  console.log('✅ Environment validation passed\n');
}

/**
 * Clean and regenerate dependencies
 */
function fixDependencies() {
  console.log('🧹 Cleaning and regenerating dependencies...');
  
  try {
    // Change to package directory
    process.chdir(PACKAGE_PATH);
    
    // Clean npm cache
    console.log('  Cleaning npm cache...');
    execSync('npm cache clean --force', { stdio: 'inherit' });
    
    // Remove node_modules and lock file
    console.log('  Removing node_modules and package-lock.json...');
    if (fs.existsSync('node_modules')) {
      execSync('rm -rf node_modules', { stdio: 'inherit' });
    }
    if (fs.existsSync('package-lock.json')) {
      fs.unlinkSync('package-lock.json');
    }
    
    // Install dependencies fresh
    console.log('  Installing dependencies...');
    execSync('npm install', { stdio: 'inherit' });
    
    // Verify installation
    console.log('  Verifying installation...');
    execSync('npm ci', { stdio: 'inherit' });
    
    console.log('✅ Dependencies fixed successfully\n');
    
  } catch (error) {
    console.error(`❌ Failed to fix dependencies: ${error.message}`);
    process.exit(1);
  } finally {
    // Return to original directory
    process.chdir('../../..');
  }
}

/**
 * Run basic tests to verify everything works
 */
function runTests() {
  console.log('🧪 Running basic tests...');
  
  try {
    process.chdir(PACKAGE_PATH);
    
    console.log('  Running linter...');
    execSync('npm run lint:check', { stdio: 'inherit' });
    
    console.log('  Running tests...');
    execSync('npm test', { stdio: 'inherit' });
    
    console.log('  Building package...');
    execSync('npm run build', { stdio: 'inherit' });
    
    // Check if TypeScript declarations were generated
    if (!fs.existsSync('dist') || !fs.readdirSync('dist').some(file => file.endsWith('.d.ts'))) {
      throw new Error('TypeScript declarations not generated');
    }
    
    console.log('✅ All tests passed\n');
    
  } catch (error) {
    console.error(`❌ Tests failed: ${error.message}`);
    console.log('Please fix the issues before proceeding with CI/CD.');
    process.exit(1);
  } finally {
    // Return to original directory
    process.chdir('../../..');
  }
}

/**
 * Check for security vulnerabilities
 */
function checkSecurity() {
  console.log('🔒 Checking for security vulnerabilities...');
  
  try {
    process.chdir(PACKAGE_PATH);
    
    // Run security audit
    execSync('npm audit --audit-level=moderate', { stdio: 'inherit' });
    
    console.log('✅ No security vulnerabilities found\n');
    
  } catch (error) {
    console.log('⚠️ Security vulnerabilities detected');
    console.log('Attempting to fix automatically...');
    
    try {
      execSync('npm audit fix', { stdio: 'inherit' });
      console.log('✅ Security fixes applied\n');
    } catch (fixError) {
      console.log('❌ Could not automatically fix all vulnerabilities');
      console.log('Please review and fix manually\n');
    }
  } finally {
    // Return to original directory
    process.chdir('../../..');
  }
}

/**
 * Generate summary report
 */
function generateSummary() {
  console.log('📋 Dependency Fix Summary\n');
  
  console.log('✅ Completed Actions:');
  console.log('  - Cleaned npm cache');
  console.log('  - Removed old node_modules and package-lock.json');
  console.log('  - Reinstalled dependencies from scratch');
  console.log('  - Verified installation with npm ci');
  console.log('  - Ran linting, tests, and build verification');
  console.log('  - Checked for security vulnerabilities\n');
  
  console.log('🚀 Next Steps:');
  console.log('  1. Commit the updated package-lock.json if changes were made');
  console.log('  2. Test the CI/CD pipeline with a test commit');
  console.log('  3. Monitor GitHub Actions for successful execution\n');
  
  console.log('💡 Tips:');
  console.log('  - Use conventional commit messages for proper versioning');
  console.log('  - Run this script if you encounter dependency issues');
  console.log('  - Keep dependencies updated regularly\n');
  
  console.log('✨ Dependencies are now ready for CI/CD pipeline!');
}

/**
 * Main execution
 */
function main() {
  try {
    validateEnvironment();
    fixDependencies();
    runTests();
    checkSecurity();
    generateSummary();
  } catch (error) {
    console.error(`\n❌ Dependency fix failed: ${error.message}`);
    process.exit(1);
  }
}

// Run the fix if this script is executed directly
if (require.main === module) {
  main();
}

module.exports = {
  validateEnvironment,
  fixDependencies,
  runTests,
  checkSecurity,
  generateSummary
};
