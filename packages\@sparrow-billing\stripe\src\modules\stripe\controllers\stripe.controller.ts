import {
  Controller,
  Post,
  Body,
  Get,
  Param,
  Put,
  Delete,
  Headers,
  Req,
  HttpStatus,
  HttpException,
} from '@nestjs/common';
import { StripeService } from '../services/stripe.service';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBody,
} from '@nestjs/swagger';
import {
  CreateCustomerDto,
  CustomerResponseDto,
  SetupIntentDto,
  SetupIntentResponseDto,
  PublicKeyResponseDto,
  CreateSubscriptionDto,
  SubscriptionResponseDto,
  UpdateSubscriptionDto,
  WebhookEventDto,
  CancelSubscriptionDto,
  ReactivateSubscriptionDto,
} from '../dto/stripe.dto';
import { Request } from 'express';

@ApiTags('stripe')
@Controller('stripe')
export class StripeController {
  constructor(private readonly stripeService: StripeService) {}

  @Get('config')
  @ApiOperation({
    summary: 'Get Stripe public key for frontend initialization',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns the Stripe publishable key',
    type: PublicKeyResponseDto,
  })
  getConfig(): PublicKeyResponseDto {
    return {
      publishableKey: this.stripeService.getPublicKey(),
    };
  }

  @Post('customers')
  @ApiOperation({ summary: 'Create a new Stripe customer' })
  @ApiResponse({
    status: 201,
    description: 'Customer created successfully',
    type: CustomerResponseDto,
  })
  async createCustomer(
    @Body() createCustomerDto: CreateCustomerDto,
  ): Promise<CustomerResponseDto> {
    const customer = await this.stripeService.createCustomer(
      createCustomerDto.email,
      createCustomerDto.metadata,
    );
    return { customer };
  }

  @Get('customers/:id')
  @ApiOperation({ summary: 'Get a Stripe customer by ID' })
  @ApiParam({
    name: 'id',
    description: 'Stripe customer ID',
    example: 'cus_12345',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns the Stripe customer',
    type: CustomerResponseDto,
  })
  @ApiResponse({ status: 404, description: 'Customer not found' })
  async getCustomer(
    @Param('id') customerId: string,
  ): Promise<CustomerResponseDto> {
    const customer = await this.stripeService.getCustomer(customerId);
    return { customer };
  }

  @Post('setup-intents')
  @ApiOperation({
    summary: 'Create a setup intent for adding a payment method',
    description:
      'Creates a SetupIntent that will be used with Stripe Elements to securely collect payment method details',
  })
  @ApiResponse({
    status: 201,
    description: 'Setup intent created successfully',
    type: SetupIntentResponseDto,
  })
  async createSetupIntent(
    @Body() setupIntentDto: SetupIntentDto,
  ): Promise<SetupIntentResponseDto> {
    const setupIntent = await this.stripeService.createSetupIntent(
      setupIntentDto.customerId,
    );
    return {
      clientSecret: setupIntent.client_secret,
    };
  }

  @Post('subscriptions')
  @ApiOperation({ summary: 'Create a new subscription for a customer' })
  @ApiBody({ type: CreateSubscriptionDto })
  @ApiResponse({
    status: 201,
    description: 'Subscription created successfully',
    type: SubscriptionResponseDto,
  })
  async createSubscription(
    @Body() createSubscriptionDto: CreateSubscriptionDto,
  ): Promise<SubscriptionResponseDto> {
    const subscription = await this.stripeService.createSubscription(
      createSubscriptionDto.customerId,
      createSubscriptionDto.priceId,
      createSubscriptionDto.paymentMethodId,
      createSubscriptionDto.metadata,
    );
    return { subscription };
  }

  /**
   * Get subscription by ID
   */
  @Get('subscriptions/:id')
  @ApiOperation({ summary: 'Get a subscription by ID' })
  @ApiParam({
    name: 'id',
    description: 'Stripe subscription ID',
    example: 'sub_12345',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns the subscription details',
    type: SubscriptionResponseDto,
  })
  @ApiResponse({ status: 404, description: 'Subscription not found' })
  async getSubscription(
    @Param('id') subscriptionId: string,
  ): Promise<SubscriptionResponseDto> {
    const subscription =
      await this.stripeService.getSubscription(subscriptionId);
    return { subscription };
  }

  @Get('subscriptions/customer/:customerId')
  @ApiOperation({ summary: 'Get all subscriptions for a customer' })
  @ApiParam({
    name: 'customerId',
    description: 'Stripe customer ID',
    example: 'cus_12345',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns all subscriptions for the customer',
    type: [SubscriptionResponseDto],
  })
  async getCustomerSubscriptions(
    @Param('customerId') customerId: string,
  ): Promise<{ subscriptions: any[] }> {
    const subscriptions =
      await this.stripeService.getCustomerSubscriptions(customerId);
    return { subscriptions };
  }

  @Put('subscriptions/:id')
  @ApiOperation({
    summary: 'Update a subscription',
    description:
      'Updates a subscription by changing the price (plan) or payment method',
  })
  @ApiParam({
    name: 'id',
    description: 'Stripe subscription ID',
    example: 'sub_12345',
  })
  @ApiBody({ type: UpdateSubscriptionDto })
  @ApiResponse({
    status: 200,
    description: 'Subscription updated successfully',
    type: SubscriptionResponseDto,
  })
  @ApiResponse({ status: 404, description: 'Subscription not found' })
  async updateSubscription(
    @Param('id') subscriptionId: string,
    @Body() updateSubscriptionDto: UpdateSubscriptionDto,
  ): Promise<SubscriptionResponseDto> {
    // Update subscription with new price and possibly new payment method
    const subscription = await this.stripeService.updateSubscription(
      subscriptionId,
      updateSubscriptionDto.priceId,
      updateSubscriptionDto.metadata,
      updateSubscriptionDto.paymentMethodId,
    );

    return { subscription };
  }

  @Delete('subscriptions/:id')
  @ApiOperation({
    summary: 'Cancel a subscription',
    description:
      'Cancels a subscription at period end or immediately based on the provided options',
  })
  @ApiParam({
    name: 'id',
    description: 'Stripe subscription ID',
    example: 'sub_12345',
  })
  @ApiBody({ type: CancelSubscriptionDto })
  @ApiResponse({
    status: 200,
    description: 'Subscription cancelled successfully',
    type: SubscriptionResponseDto,
  })
  @ApiResponse({ status: 404, description: 'Subscription not found' })
  async cancelSubscription(
    @Param('id') subscriptionId: string,
    @Body() cancelSubscriptionDto: CancelSubscriptionDto,
  ): Promise<SubscriptionResponseDto> {
    const subscription = await this.stripeService.cancelSubscription(
      subscriptionId,
      cancelSubscriptionDto.cancelImmediately || false,
    );
    return { subscription };
  }

  @Post('subscriptions/:id/reactivate')
  @ApiOperation({
    summary: 'Reactivate a cancelled subscription',
    description:
      'Reactivates a subscription that was previously cancelled but is still within the current billing period',
  })
  @ApiParam({
    name: 'id',
    description: 'Stripe subscription ID',
    example: 'sub_12345',
  })
  @ApiBody({ type: ReactivateSubscriptionDto })
  @ApiResponse({
    status: 200,
    description: 'Subscription reactivated successfully',
    type: SubscriptionResponseDto,
  })
  @ApiResponse({ status: 404, description: 'Subscription not found' })
  @ApiResponse({
    status: 400,
    description: 'Subscription cannot be reactivated',
  })
  async reactivateSubscription(
    @Param('id') subscriptionId: string,
    @Body() reactivateDto: ReactivateSubscriptionDto,
  ): Promise<SubscriptionResponseDto> {
    const subscription = await this.stripeService.reactivateSubscription(
      subscriptionId,
      reactivateDto.metadata,
    );
    return { subscription };
  }

  @Get('plans')
  @ApiOperation({ summary: 'Get all available subscription plans/prices' })
  @ApiResponse({
    status: 200,
    description: 'Returns all active subscription plans',
  })
  async getPlans(): Promise<{ plans: any[] }> {
    const plans = await this.stripeService.getPlans();
    return { plans };
  }

  @Get('invoices/customer/:customerId')
  @ApiOperation({ summary: 'Get all invoices for a customer' })
  @ApiParam({
    name: 'customerId',
    description: 'Stripe customer ID',
    example: 'cus_12345',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns all invoices for the customer',
  })
  async getCustomerInvoices(
    @Param('customerId') customerId: string,
  ): Promise<{ invoices: any[] }> {
    const invoices = await this.stripeService.getCustomerInvoices(customerId);
    return { invoices };
  }

  @Post('webhooks')
  @ApiOperation({
    summary: 'Handle Stripe webhook events',
    description:
      'Processes Stripe webhook events such as subscription updates, payment successes/failures, etc.',
  })
  @ApiResponse({
    status: 200,
    description: 'Webhook event processed successfully',
  })
  async handleWebhook(
    @Body() payload: any,
    @Headers('stripe-signature') signature: string,
    @Req() request: Request,
  ): Promise<{ received: boolean }> {
    try {
      const event = await this.stripeService.constructEventFromPayload(
        payload,
        signature,
      );
      await this.stripeService.handleWebhookEvent(event);
      return { received: true };
    } catch (err) {
      throw new HttpException(
        'Webhook error: ' + err.message,
        HttpStatus.BAD_REQUEST,
      );
    }
  }
}
