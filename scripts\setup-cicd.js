#!/usr/bin/env node

/**
 * CI/CD Setup Script for @sparrowapp-dev/stripe-billing
 * 
 * This script helps set up the CI/CD pipeline and ensures all necessary
 * configurations are in place for automated releases.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 Setting up CI/CD pipeline for @sparrowapp-dev/stripe-billing...\n');

// Configuration
const PACKAGE_PATH = 'packages/@sparrow-billing/stripe';
const GITHUB_WORKFLOWS_PATH = '.github/workflows';

/**
 * Check if we're in the correct directory
 */
function validateEnvironment() {
  console.log('📍 Validating environment...');
  
  if (!fs.existsSync(PACKAGE_PATH)) {
    console.error(`❌ Package directory not found: ${PACKAGE_PATH}`);
    console.error('Please run this script from the repository root.');
    process.exit(1);
  }
  
  if (!fs.existsSync(path.join(PACKAGE_PATH, 'package.json'))) {
    console.error(`❌ package.json not found in ${PACKAGE_PATH}`);
    process.exit(1);
  }
  
  console.log('✅ Environment validation passed\n');
}

/**
 * Check if GitHub workflows exist
 */
function checkWorkflows() {
  console.log('🔍 Checking GitHub workflows...');
  
  const workflows = [
    'release.yml',
    'pr-check.yml',
    'dependency-update.yml'
  ];
  
  const missingWorkflows = workflows.filter(workflow => 
    !fs.existsSync(path.join(GITHUB_WORKFLOWS_PATH, workflow))
  );
  
  if (missingWorkflows.length > 0) {
    console.log(`⚠️ Missing workflows: ${missingWorkflows.join(', ')}`);
    console.log('Please ensure all workflow files are in place.');
  } else {
    console.log('✅ All GitHub workflows found');
  }
  
  console.log('');
}

/**
 * Validate package.json configuration
 */
function validatePackageJson() {
  console.log('📦 Validating package.json configuration...');
  
  const packageJsonPath = path.join(PACKAGE_PATH, 'package.json');
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  
  // Check required fields
  const requiredFields = {
    'name': '@sparrowapp-dev/stripe-billing',
    'repository.type': 'git',
    'repository.url': 'git+https://github.com/sparrowapp-dev/sparrow-packages.git',
    'publishConfig.registry': 'https://npm.pkg.github.com/',
    'publishConfig.access': 'restricted'
  };
  
  let hasErrors = false;
  
  for (const [field, expectedValue] of Object.entries(requiredFields)) {
    const fieldPath = field.split('.');
    let currentValue = packageJson;
    
    for (const key of fieldPath) {
      currentValue = currentValue?.[key];
    }
    
    if (currentValue !== expectedValue) {
      console.log(`❌ ${field}: expected "${expectedValue}", got "${currentValue}"`);
      hasErrors = true;
    }
  }
  
  // Check required scripts
  const requiredScripts = [
    'build',
    'test',
    'lint',
    'format',
    'release'
  ];
  
  for (const script of requiredScripts) {
    if (!packageJson.scripts?.[script]) {
      console.log(`❌ Missing script: ${script}`);
      hasErrors = true;
    }
  }
  
  // Check semantic-release dependencies
  const semanticReleaseDeps = [
    '@semantic-release/changelog',
    '@semantic-release/commit-analyzer',
    '@semantic-release/git',
    '@semantic-release/github',
    '@semantic-release/npm',
    '@semantic-release/release-notes-generator',
    'semantic-release'
  ];
  
  for (const dep of semanticReleaseDeps) {
    if (!packageJson.devDependencies?.[dep]) {
      console.log(`❌ Missing dev dependency: ${dep}`);
      hasErrors = true;
    }
  }
  
  if (!hasErrors) {
    console.log('✅ package.json configuration is valid');
  }
  
  console.log('');
}

/**
 * Check semantic-release configuration
 */
function checkSemanticReleaseConfig() {
  console.log('🔧 Checking semantic-release configuration...');
  
  const releaseConfigPath = path.join(PACKAGE_PATH, '.releaserc.json');
  
  if (!fs.existsSync(releaseConfigPath)) {
    console.log('❌ .releaserc.json not found');
    return;
  }
  
  try {
    const config = JSON.parse(fs.readFileSync(releaseConfigPath, 'utf8'));
    
    // Check required plugins
    const requiredPlugins = [
      '@semantic-release/commit-analyzer',
      '@semantic-release/release-notes-generator',
      '@semantic-release/changelog',
      '@semantic-release/npm',
      '@semantic-release/github',
      '@semantic-release/git'
    ];
    
    const configuredPlugins = config.plugins?.map(plugin => 
      Array.isArray(plugin) ? plugin[0] : plugin
    ) || [];
    
    const missingPlugins = requiredPlugins.filter(plugin => 
      !configuredPlugins.includes(plugin)
    );
    
    if (missingPlugins.length > 0) {
      console.log(`❌ Missing semantic-release plugins: ${missingPlugins.join(', ')}`);
    } else {
      console.log('✅ Semantic-release configuration is valid');
    }
  } catch (error) {
    console.log(`❌ Error reading .releaserc.json: ${error.message}`);
  }
  
  console.log('');
}

/**
 * Test the build process
 */
function testBuild() {
  console.log('🔨 Testing build process...');
  
  try {
    process.chdir(PACKAGE_PATH);
    
    console.log('  Installing dependencies...');
    execSync('npm ci', { stdio: 'pipe' });
    
    console.log('  Running linter...');
    execSync('npm run lint:check', { stdio: 'pipe' });
    
    console.log('  Running tests...');
    execSync('npm test', { stdio: 'pipe' });
    
    console.log('  Building package...');
    execSync('npm run build', { stdio: 'pipe' });
    
    // Check if TypeScript declarations were generated
    if (!fs.existsSync('dist') || !fs.readdirSync('dist').some(file => file.endsWith('.d.ts'))) {
      throw new Error('TypeScript declarations not generated');
    }
    
    console.log('✅ Build process completed successfully');
    
  } catch (error) {
    console.log(`❌ Build process failed: ${error.message}`);
    console.log('Please fix the build issues before proceeding.');
  } finally {
    // Return to original directory
    process.chdir('../../..');
  }
  
  console.log('');
}

/**
 * Generate setup summary
 */
function generateSummary() {
  console.log('📋 CI/CD Setup Summary\n');
  
  console.log('🔧 Configuration Files:');
  console.log('  ✅ .github/workflows/release.yml - Main release pipeline');
  console.log('  ✅ .github/workflows/pr-check.yml - PR quality checks');
  console.log('  ✅ .github/workflows/dependency-update.yml - Automated dependency updates');
  console.log('  ✅ .releaserc.json - Semantic-release configuration');
  console.log('  ✅ CI_CD_GUIDE.md - Comprehensive documentation\n');
  
  console.log('🚀 Pipeline Features:');
  console.log('  ✅ Automated semantic versioning');
  console.log('  ✅ Comprehensive release notes generation');
  console.log('  ✅ Multi-Node.js version testing');
  console.log('  ✅ TypeScript declaration verification');
  console.log('  ✅ Security vulnerability scanning');
  console.log('  ✅ Automated dependency updates');
  console.log('  ✅ GitHub Packages publishing');
  console.log('  ✅ GitHub Releases with assets\n');
  
  console.log('📚 Next Steps:');
  console.log('  1. Review the CI_CD_GUIDE.md for detailed usage instructions');
  console.log('  2. Ensure your GitHub repository has the necessary permissions');
  console.log('  3. Test the pipeline by creating a PR with conventional commits');
  console.log('  4. Monitor the Actions tab for pipeline execution\n');
  
  console.log('🎯 Commit Message Examples:');
  console.log('  feat: add new payment method support (minor version bump)');
  console.log('  fix: resolve validation issue (patch version bump)');
  console.log('  feat!: redesign API interface (major version bump)');
  console.log('  docs: update README (no version bump)\n');
  
  console.log('✨ CI/CD pipeline setup completed successfully!');
}

/**
 * Main execution
 */
function main() {
  try {
    validateEnvironment();
    checkWorkflows();
    validatePackageJson();
    checkSemanticReleaseConfig();
    testBuild();
    generateSummary();
  } catch (error) {
    console.error(`\n❌ Setup failed: ${error.message}`);
    process.exit(1);
  }
}

// Run the setup if this script is executed directly
if (require.main === module) {
  main();
}

module.exports = {
  validateEnvironment,
  checkWorkflows,
  validatePackageJson,
  checkSemanticReleaseConfig,
  testBuild,
  generateSummary
};
