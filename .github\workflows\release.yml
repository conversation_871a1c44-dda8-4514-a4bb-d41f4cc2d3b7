name: Release and Publish Package

on:
  push:
    branches:
      - main
      - master
    paths:
      - 'packages/@sparrow-billing/stripe/**'
  workflow_dispatch:
    inputs:
      release_type:
        description: 'Release type (patch, minor, major)'
        required: false
        default: 'patch'
        type: choice
        options:
          - patch
          - minor
          - major

env:
  PACKAGE_PATH: packages/@sparrow-billing/stripe
  NODE_VERSION: '20'

jobs:
  test:
    name: Run Tests
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ${{ env.PACKAGE_PATH }}
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: ${{ env.PACKAGE_PATH }}/package-lock.json

      - name: Install dependencies
        run: npm ci

      - name: Run linting
        run: npm run lint

      - name: Run tests
        run: npm run test:cov

      - name: Upload coverage reports
        uses: codecov/codecov-action@v3
        with:
          file: ${{ env.PACKAGE_PATH }}/coverage/lcov.info
          flags: unittests
          name: stripe-billing-coverage

  build:
    name: Build Package
    runs-on: ubuntu-latest
    needs: test
    defaults:
      run:
        working-directory: ${{ env.PACKAGE_PATH }}
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: ${{ env.PACKAGE_PATH }}/package-lock.json

      - name: Install dependencies
        run: npm ci

      - name: Build package
        run: npm run build

      - name: Verify TypeScript declarations
        run: |
          if [ ! -d "dist" ] || [ -z "$(find dist -name '*.d.ts' -print -quit)" ]; then
            echo "Error: No TypeScript declaration files found in dist/"
            exit 1
          fi
          echo "✅ TypeScript declarations generated successfully"

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: build-artifacts
          path: ${{ env.PACKAGE_PATH }}/dist/
          retention-days: 1

  release:
    name: Create Release and Publish
    runs-on: ubuntu-latest
    needs: [test, build]
    defaults:
      run:
        working-directory: ${{ env.PACKAGE_PATH }}
    permissions:
      contents: write
      packages: write
      issues: write
      pull-requests: write
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: ${{ env.PACKAGE_PATH }}/package-lock.json
          registry-url: 'https://npm.pkg.github.com'
          scope: '@sparrowapp-dev'

      - name: Install dependencies
        run: npm ci

      - name: Download build artifacts
        uses: actions/download-artifact@v4
        with:
          name: build-artifacts
          path: ${{ env.PACKAGE_PATH }}/dist/

      - name: Install semantic-release
        run: |
          npm install -g semantic-release@latest
          npm install -D @semantic-release/changelog@latest
          npm install -D @semantic-release/git@latest
          npm install -D @semantic-release/github@latest
          npm install -D @semantic-release/npm@latest
          npm install -D conventional-changelog-conventionalcommits@latest

      - name: Configure Git
        run: |
          git config --global user.name "github-actions[bot]"
          git config --global user.email "github-actions[bot]@users.noreply.github.com"

      - name: Create .npmrc for GitHub Packages
        run: |
          echo "@sparrowapp-dev:registry=https://npm.pkg.github.com/" > .npmrc
          echo "//npm.pkg.github.com/:_authToken=${{ secrets.GITHUB_TOKEN }}" >> .npmrc
          echo "registry=https://registry.npmjs.org/" >> .npmrc

      - name: Run semantic-release
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          NODE_AUTH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          NPM_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: npx semantic-release

      - name: Get package version
        id: package-version
        run: |
          VERSION=$(node -p "require('./package.json').version")
          echo "version=$VERSION" >> $GITHUB_OUTPUT
          echo "Package version: $VERSION"

      - name: Create GitHub Release
        if: steps.package-version.outputs.version
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: v${{ steps.package-version.outputs.version }}
          release_name: Release v${{ steps.package-version.outputs.version }}
          body: |
            ## 🚀 Release v${{ steps.package-version.outputs.version }}
            
            ### 📦 Package Information
            - **Package**: @sparrowapp-dev/stripe-billing
            - **Version**: ${{ steps.package-version.outputs.version }}
            - **Registry**: GitHub Packages
            
            ### 📥 Installation
            ```bash
            npm install @sparrowapp-dev/stripe-billing@${{ steps.package-version.outputs.version }}
            ```
            
            ### 🔗 Links
            - [Package on GitHub Packages](https://github.com/sparrowapp-dev/sparrow-packages/packages)
            - [Documentation](https://github.com/sparrowapp-dev/sparrow-packages/tree/main/packages/@sparrow-billing/stripe#readme)
            
            ---
            *This release was automatically generated by GitHub Actions*
          draft: false
          prerelease: false

  notify:
    name: Notify Release
    runs-on: ubuntu-latest
    needs: release
    if: always()
    
    steps:
      - name: Notify success
        if: needs.release.result == 'success'
        run: |
          echo "✅ Package successfully released and published!"
          echo "🎉 Check GitHub Packages for the new version"

      - name: Notify failure
        if: needs.release.result == 'failure'
        run: |
          echo "❌ Release failed!"
          echo "Please check the logs for more information"
          exit 1
