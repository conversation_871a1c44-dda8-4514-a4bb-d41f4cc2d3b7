import {
  Inject,
  Injectable,
  Logger,
  Optional,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import Stripe from 'stripe';

interface StripeOptions {
  secretKey?: string;
  publishableKey?: string;
  webhookSecret?: string;
}

@Injectable()
export class StripeService {
  private stripe: Stripe;
  private readonly logger = new Logger(StripeService.name);
  private readonly publishableKey?: string;
  private readonly webhookSecret?: string;

  constructor(
    private configService: ConfigService,
    @Optional() @Inject('STRIPE_OPTIONS') private options?: StripeOptions,
  ) {
    // Use options if provided, otherwise fall back to config service
    const secretKey =
      this.options?.secretKey ||
      this.configService.get<string>('stripe.secretKey');
    this.publishableKey =
      this.options?.publishableKey ||
      this.configService.get<string>('stripe.publishableKey');
    this.webhookSecret =
      this.options?.webhookSecret ||
      this.configService.get<string>('stripe.webhookSecret');

    if (!secretKey) {
      this.logger.error('Stripe secret key is not defined in configuration');
      throw new Error('Stripe secret key is not defined');
    }

    this.stripe = new Stripe(secretKey, {
      apiVersion: '2025-04-30.basil',
    });
  }

  getPublicKey(): string {
    if (!this.publishableKey) {
      this.logger.error(
        'Stripe publishable key is not defined in configuration',
      );
      throw new Error('Stripe publishable key is not defined');
    }
    return this.publishableKey;
  }

  getWebhookSecret(): string {
    if (!this.webhookSecret) {
      this.logger.error(
        'Stripe webhook secret is not defined in configuration',
      );
      throw new Error('Stripe webhook secret is not defined');
    }
    return this.webhookSecret;
  }

  async createSetupIntent(customerId: string): Promise<Stripe.SetupIntent> {
    return this.stripe.setupIntents.create({
      customer: customerId,
      payment_method_types: ['card'],
    });
  }

  async createCustomer(
    email: string,
    metadata?: Record<string, string>,
  ): Promise<Stripe.Customer> {
    return this.stripe.customers.create({
      email,
      metadata,
    });
  }

  async getCustomer(customerId: string): Promise<Stripe.Customer> {
    return this.stripe.customers.retrieve(
      customerId,
    ) as Promise<Stripe.Customer>;
  }

  getStripeInstance(): Stripe {
    return this.stripe;
  }

  // Subscription-related methods

  /**
   * Create a new subscription for a customer
   */
  async createSubscription(
    customerId: string,
    priceId: string,
    paymentMethodId: string,
    metadata?: Record<string, string>,
  ): Promise<Stripe.Subscription> {
    try {
      // First attach the payment method to the customer if it's not already
      await this.stripe.paymentMethods.attach(paymentMethodId, {
        customer: customerId,
      });

      // Set the payment method as the default
      await this.stripe.customers.update(customerId, {
        invoice_settings: {
          default_payment_method: paymentMethodId,
        },
      });

      // Create the subscription
      const subscription = await this.stripe.subscriptions.create({
        customer: customerId,
        items: [{ price: priceId }],
        default_payment_method: paymentMethodId,
        expand: ['latest_invoice.payment_intent'],
        metadata,
        payment_behavior: 'allow_incomplete',
        payment_settings: {
          payment_method_types: ['card'],
          save_default_payment_method: 'on_subscription',
        },
      });

      return subscription;
    } catch (error) {
      this.logger.error(
        `Error creating subscription: ${error.message}`,
        error.stack,
      );
      throw new BadRequestException(
        `Failed to create subscription: ${error.message}`,
      );
    }
  }

  /**
   * Get subscription by ID
   */
  async getSubscription(subscriptionId: string): Promise<Stripe.Subscription> {
    try {
      return await this.stripe.subscriptions.retrieve(subscriptionId);
    } catch (error) {
      if (error.code === 'resource_missing') {
        throw new NotFoundException('Subscription not found');
      }
      throw new BadRequestException(
        `Failed to retrieve subscription: ${error.message}`,
      );
    }
  }

  /**
   * Get all subscriptions for a customer
   */
  async getCustomerSubscriptions(
    customerId: string,
  ): Promise<Stripe.Subscription[]> {
    try {
      const subscriptions = await this.stripe.subscriptions.list({
        customer: customerId,
        status: 'all', // Include active, past_due, canceled, etc.
        expand: ['data.default_payment_method'],
      });
      return subscriptions.data;
    } catch (error) {
      this.logger.error(
        `Error retrieving customer subscriptions: ${error.message}`,
        error.stack,
      );
      throw new BadRequestException(
        `Failed to retrieve subscriptions: ${error.message}`,
      );
    }
  }

  /**
   * Update a subscription (change plan)
   */
  async updateSubscription(
    subscriptionId: string,
    priceId: string,
    metadata?: Record<string, string>,
  ): Promise<Stripe.Subscription> {
    try {
      // Get current subscription to find the subscription item ID
      const subscription =
        await this.stripe.subscriptions.retrieve(subscriptionId);
      const subscriptionItemId = subscription.items.data[0].id;

      // Update the subscription
      return await this.stripe.subscriptions.update(subscriptionId, {
        items: [
          {
            id: subscriptionItemId,
            price: priceId,
          },
        ],
        proration_behavior: 'create_prorations',
        billing_cycle_anchor: 'now',
        metadata,
      });
    } catch (error) {
      if (error.code === 'resource_missing') {
        throw new NotFoundException('Subscription not found');
      }
      throw new BadRequestException(
        `Failed to update subscription: ${error.message}`,
      );
    }
  }

  /**
   * Cancel a subscription
   */
  async cancelSubscription(
    subscriptionId: string,
    cancelImmediately: boolean = false,
  ): Promise<Stripe.Subscription> {
    try {
      if (cancelImmediately) {
        // Cancel immediately
        return await this.stripe.subscriptions.cancel(subscriptionId);
      } else {
        // Cancel at period end
        return await this.stripe.subscriptions.update(subscriptionId, {
          cancel_at_period_end: true,
        });
      }
    } catch (error) {
      if (error.code === 'resource_missing') {
        throw new NotFoundException('Subscription not found');
      }
      throw new BadRequestException(
        `Failed to cancel subscription: ${error.message}`,
      );
    }
  }

  /**
   * Reactivate a subscription that was set to cancel at period end
   */
  async reactivateSubscription(
    subscriptionId: string,
    metadata?: Record<string, string>,
  ): Promise<Stripe.Subscription> {
    try {
      // Check if subscription can be reactivated
      const subscription =
        await this.stripe.subscriptions.retrieve(subscriptionId);

      if (subscription.status === 'canceled') {
        throw new BadRequestException(
          'Subscription has already been canceled and cannot be reactivated',
        );
      }

      if (!subscription.cancel_at_period_end) {
        throw new BadRequestException(
          'Subscription is not set to cancel at period end',
        );
      }

      // Reactivate by setting cancel_at_period_end to false
      return await this.stripe.subscriptions.update(subscriptionId, {
        cancel_at_period_end: false,
        metadata,
      });
    } catch (error) {
      if (error.code === 'resource_missing') {
        throw new NotFoundException('Subscription not found');
      }
      throw error; // Re-throw BadRequestException or other errors
    }
  }

  /**
   * Get all available subscription plans/prices
   */
  async getPlans(): Promise<Stripe.Price[]> {
    try {
      const prices = await this.stripe.prices.list({
        active: true,
        expand: ['data.product'],
      });
      return prices.data;
    } catch (error) {
      this.logger.error(
        `Error retrieving plans: ${error.message}`,
        error.stack,
      );
      throw new BadRequestException(
        `Failed to retrieve plans: ${error.message}`,
      );
    }
  }

  /**
   * Get all invoices for a customer
   */
  async getCustomerInvoices(customerId: string): Promise<Stripe.Invoice[]> {
    try {
      const invoices = await this.stripe.invoices.list({
        customer: customerId,
      });
      return invoices.data;
    } catch (error) {
      this.logger.error(
        `Error retrieving customer invoices: ${error.message}`,
        error.stack,
      );
      throw new BadRequestException(
        `Failed to retrieve invoices: ${error.message}`,
      );
    }
  }

  /**
   * Construct a Stripe webhook event from the payload and signature
   */
  async constructEventFromPayload(
    payload: any,
    signature: string,
  ): Promise<Stripe.Event> {
    try {
      const webhookSecret = this.getWebhookSecret();
      return this.stripe.webhooks.constructEvent(
        JSON.stringify(payload),
        signature,
        webhookSecret,
      );
    } catch (error) {
      this.logger.error(
        `Webhook signature verification failed: ${error.message}`,
      );
      throw new BadRequestException(
        `Webhook signature verification failed: ${error.message}`,
      );
    }
  }

  /**
   * Handle Stripe webhook events
   */
  async handleWebhookEvent(event: Stripe.Event): Promise<void> {
    this.logger.log(
      `Processing webhook event ${event.id} of type ${event.type}`,
    );

    try {
      switch (event.type) {
        case 'customer.subscription.created':
        case 'customer.subscription.updated':
        case 'customer.subscription.deleted':
          await this.handleSubscriptionEvent(event);
          break;
        case 'invoice.payment_succeeded':
        case 'invoice.payment_failed':
          await this.handleInvoiceEvent(event);
          break;
        // Add more event handlers as needed
        default:
          this.logger.log(`Unhandled event type: ${event.type}`);
      }
    } catch (error) {
      this.logger.error(
        `Error processing webhook event: ${error.message}`,
        error.stack,
      );
      throw new BadRequestException(
        `Error processing webhook event: ${error.message}`,
      );
    }
  }

  /**
   * Handle subscription-related webhook events
   */
  private async handleSubscriptionEvent(event: Stripe.Event): Promise<void> {
    const subscription = event.data.object as Stripe.Subscription;
    this.logger.log(
      `Subscription ${subscription.id} is ${subscription.status}`,
    );

    // Implement your business logic here
    // For example, update your database with subscription status
  }

  /**
   * Handle invoice-related webhook events
   */
  private async handleInvoiceEvent(event: Stripe.Event): Promise<void> {
    const invoice = event.data.object as Stripe.Invoice;
    this.logger.log(
      `Invoice ${invoice.id} payment ${event.type === 'invoice.payment_succeeded' ? 'succeeded' : 'failed'}`,
    );

    // Implement your business logic here
    // For example, send email notification to the customer
  }
}
