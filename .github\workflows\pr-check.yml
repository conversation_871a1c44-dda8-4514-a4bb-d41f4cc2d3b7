name: PR Quality Check

on:
  pull_request:
    branches:
      - main
      - master
    paths:
      - 'packages/@sparrow-billing/stripe/**'
  workflow_dispatch:

env:
  PACKAGE_PATH: packages/@sparrow-billing/stripe
  NODE_VERSION: '20'

jobs:
  changes:
    name: Detect Changes
    runs-on: ubuntu-latest
    outputs:
      stripe-billing: ${{ steps.changes.outputs.stripe-billing }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Check for changes
        uses: dorny/paths-filter@v2
        id: changes
        with:
          filters: |
            stripe-billing:
              - 'packages/@sparrow-billing/stripe/**'

  lint-and-format:
    name: Lint and Format Check
    runs-on: ubuntu-latest
    needs: changes
    if: needs.changes.outputs.stripe-billing == 'true'
    defaults:
      run:
        working-directory: ${{ env.PACKAGE_PATH }}
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: ${{ env.PACKAGE_PATH }}/package-lock.json

      - name: Install dependencies
        run: npm ci

      - name: Run ESLint
        run: npm run lint

      - name: Check Prettier formatting
        run: |
          npm run format
          if [ -n "$(git status --porcelain)" ]; then
            echo "❌ Code is not properly formatted. Please run 'npm run format'"
            git diff
            exit 1
          else
            echo "✅ Code is properly formatted"
          fi

  test:
    name: Run Tests
    runs-on: ubuntu-latest
    needs: changes
    if: needs.changes.outputs.stripe-billing == 'true'
    defaults:
      run:
        working-directory: ${{ env.PACKAGE_PATH }}
    
    strategy:
      matrix:
        node-version: ['18', '20', '21']
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          cache: 'npm'
          cache-dependency-path: ${{ env.PACKAGE_PATH }}/package-lock.json

      - name: Install dependencies
        run: npm ci

      - name: Run unit tests
        run: npm run test

      - name: Run tests with coverage
        if: matrix.node-version == '20'
        run: npm run test:cov

      - name: Upload coverage to Codecov
        if: matrix.node-version == '20'
        uses: codecov/codecov-action@v3
        with:
          file: ${{ env.PACKAGE_PATH }}/coverage/lcov.info
          flags: unittests
          name: stripe-billing-pr-coverage

  build:
    name: Build Package
    runs-on: ubuntu-latest
    needs: changes
    if: needs.changes.outputs.stripe-billing == 'true'
    defaults:
      run:
        working-directory: ${{ env.PACKAGE_PATH }}
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: ${{ env.PACKAGE_PATH }}/package-lock.json

      - name: Install dependencies
        run: npm ci

      - name: Build package
        run: npm run build

      - name: Verify build output
        run: |
          if [ ! -d "dist" ]; then
            echo "❌ Build failed: dist directory not found"
            exit 1
          fi
          
          if [ -z "$(find dist -name '*.d.ts' -print -quit)" ]; then
            echo "❌ Build failed: No TypeScript declaration files found"
            exit 1
          fi
          
          if [ ! -f "dist/index.js" ]; then
            echo "❌ Build failed: Main entry point not found"
            exit 1
          fi
          
          echo "✅ Build successful with TypeScript declarations"

      - name: Test package installation
        run: |
          # Create a temporary directory to test package installation
          mkdir -p /tmp/test-install
          cd /tmp/test-install
          npm init -y
          
          # Pack the built package
          cd $GITHUB_WORKSPACE/${{ env.PACKAGE_PATH }}
          npm pack
          
          # Install the packed package
          cd /tmp/test-install
          npm install $GITHUB_WORKSPACE/${{ env.PACKAGE_PATH }}/*.tgz
          
          echo "✅ Package installation test successful"

  security:
    name: Security Audit
    runs-on: ubuntu-latest
    needs: changes
    if: needs.changes.outputs.stripe-billing == 'true'
    defaults:
      run:
        working-directory: ${{ env.PACKAGE_PATH }}
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: ${{ env.PACKAGE_PATH }}/package-lock.json

      - name: Install dependencies
        run: npm ci

      - name: Run security audit
        run: |
          npm audit --audit-level=moderate
          if [ $? -ne 0 ]; then
            echo "⚠️ Security vulnerabilities found. Please review and fix."
            npm audit --audit-level=moderate --json > audit-results.json
            exit 1
          else
            echo "✅ No security vulnerabilities found"
          fi

  validate-pr:
    name: Validate PR
    runs-on: ubuntu-latest
    needs: [lint-and-format, test, build, security]
    if: always() && needs.changes.outputs.stripe-billing == 'true'
    
    steps:
      - name: Check all jobs status
        run: |
          if [ "${{ needs.lint-and-format.result }}" != "success" ] || \
             [ "${{ needs.test.result }}" != "success" ] || \
             [ "${{ needs.build.result }}" != "success" ] || \
             [ "${{ needs.security.result }}" != "success" ]; then
            echo "❌ One or more checks failed"
            exit 1
          else
            echo "✅ All checks passed! PR is ready for review."
          fi

  comment-pr:
    name: Comment on PR
    runs-on: ubuntu-latest
    needs: [validate-pr]
    if: always() && github.event_name == 'pull_request'
    permissions:
      pull-requests: write
    
    steps:
      - name: Comment success
        if: needs.validate-pr.result == 'success'
        uses: actions/github-script@v7
        with:
          script: |
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: '✅ **All checks passed!** This PR is ready for review.\n\n' +
                    '### Summary\n' +
                    '- ✅ Linting and formatting\n' +
                    '- ✅ Tests (Node.js 18, 20, 21)\n' +
                    '- ✅ Build and TypeScript declarations\n' +
                    '- ✅ Security audit\n\n' +
                    '*Automated comment by GitHub Actions*'
            })

      - name: Comment failure
        if: needs.validate-pr.result == 'failure'
        uses: actions/github-script@v7
        with:
          script: |
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: '❌ **Some checks failed.** Please review the failed checks and fix the issues.\n\n' +
                    'Check the [Actions tab](' + context.payload.repository.html_url + '/actions) for detailed logs.\n\n' +
                    '*Automated comment by GitHub Actions*'
            })
