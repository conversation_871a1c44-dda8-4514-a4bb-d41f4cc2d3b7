{"branches": ["main", "master"], "repositoryUrl": "https://github.com/sparrowapp-dev/sparrow-packages.git", "tagFormat": "stripe-billing-v${version}", "plugins": [["@semantic-release/commit-analyzer", {"preset": "conventionalcommits", "releaseRules": [{"type": "feat", "release": "minor"}, {"type": "fix", "release": "patch"}, {"type": "perf", "release": "patch"}, {"type": "revert", "release": "patch"}, {"type": "docs", "release": false}, {"type": "style", "release": false}, {"type": "chore", "release": false}, {"type": "refactor", "release": "patch"}, {"type": "test", "release": false}, {"type": "build", "release": false}, {"type": "ci", "release": false}, {"scope": "no-release", "release": false}, {"breaking": true, "release": "major"}], "parserOpts": {"noteKeywords": ["BREAKING CHANGE", "BREAKING CHANGES", "BREAKING"]}}], ["@semantic-release/release-notes-generator", {"preset": "conventionalcommits", "presetConfig": {"types": [{"type": "feat", "section": "🚀 Features", "hidden": false}, {"type": "fix", "section": "🐛 Bug Fixes", "hidden": false}, {"type": "perf", "section": "⚡ Performance Improvements", "hidden": false}, {"type": "revert", "section": "⏪ Reverts", "hidden": false}, {"type": "docs", "section": "📚 Documentation", "hidden": false}, {"type": "style", "section": "💎 Styles", "hidden": false}, {"type": "refactor", "section": "📦 Code Refactoring", "hidden": false}, {"type": "test", "section": "🚨 Tests", "hidden": false}, {"type": "build", "section": "🛠 Build System", "hidden": false}, {"type": "ci", "section": "⚙️ Continuous Integration", "hidden": false}, {"type": "chore", "section": "♻️ Chores", "hidden": false}]}, "writerOpts": {"commitsSort": ["subject", "scope"]}}], ["@semantic-release/changelog", {"changelogFile": "CHANGELOG.md", "changelogTitle": "# Changelog\n\nAll notable changes to the @sparrowapp-dev/stripe-billing package will be documented in this file.\n\nThe format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),\nand this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html)."}], ["@semantic-release/npm", {"npmPublish": true, "tarballDir": "dist-package"}], ["@semantic-release/github", {"assets": [{"path": "dist-package/*.tgz", "label": "Package tarball"}, {"path": "CHANGELOG.md", "label": "Changelog"}], "successComment": "🎉 This ${issue.pull_request ? 'PR is included' : 'issue has been resolved'} in version ${nextRelease.version} :tada:\n\n**Package**: @sparrowapp-dev/stripe-billing@${nextRelease.version}\n\n**Installation**:\n```bash\nnpm install @sparrowapp-dev/stripe-billing@${nextRelease.version}\n```\n\n**Release Notes**: ${releases.map(release => release.url).join(', ')}"}], ["@semantic-release/git", {"assets": ["package.json", "package-lock.json", "CHANGELOG.md"], "message": "chore(release): ${nextRelease.version} [skip ci]\n\n${nextRelease.notes}"}]]}