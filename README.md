# CI/CD Pipeline for @sparrowapp-dev/stripe-billing

[![Release](https://github.com/sparrowapp-dev/sparrow-packages/actions/workflows/release.yml/badge.svg)](https://github.com/sparrowapp-dev/sparrow-packages/actions/workflows/release.yml)
[![PR Check](https://github.com/sparrowapp-dev/sparrow-packages/actions/workflows/pr-check.yml/badge.svg)](https://github.com/sparrowapp-dev/sparrow-packages/actions/workflows/pr-check.yml)
[![Dependencies](https://github.com/sparrowapp-dev/sparrow-packages/actions/workflows/dependency-update.yml/badge.svg)](https://github.com/sparrowapp-dev/sparrow-packages/actions/workflows/dependency-update.yml)

A comprehensive CI/CD pipeline implementation for the @sparrowapp-dev/stripe-billing package that provides automated semantic versioning, comprehensive release notes generation, testing, building, and publishing to GitHub Packages.

## 🚀 Features

- ✅ **Automated Semantic Versioning** - Version bumps based on conventional commits
- ✅ **Comprehensive Release Notes** - Auto-generated with categorized changes and contributors
- ✅ **Multi-Node.js Testing** - Tests across Node.js versions 18, 20, and 21
- ✅ **TypeScript Build Verification** - Ensures proper declaration file generation
- ✅ **Security Scanning** - Automated vulnerability detection and fixes
- ✅ **GitHub Packages Publishing** - Secure authentication and automated publishing
- ✅ **GitHub Releases** - Tagged releases with assets and detailed notes
- ✅ **Dependency Management** - Weekly automated dependency updates
- ✅ **Quality Gates** - Comprehensive PR validation and code quality checks

## 📋 Quick Start

### 1. Setup Validation

Run the setup script to validate your configuration:

```bash
node scripts/setup-cicd.js
```

### 2. Create a Feature Branch

```bash
git checkout -b feature/your-feature-name
```

### 3. Make Changes with Conventional Commits

Use conventional commit messages to trigger appropriate version bumps:

```bash
# Patch release (0.1.0 → 0.1.1)
git commit -m "fix: resolve payment validation issue"

# Minor release (0.1.0 → 0.2.0)
git commit -m "feat: add Apple Pay support"

# Major release (0.1.0 → 1.0.0)
git commit -m "feat!: redesign API interface

BREAKING CHANGE: Constructor parameters have changed"
```

### 4. Push and Create PR

```bash
git push origin feature/your-feature-name
# Create PR through GitHub UI
```

### 5. Merge to Trigger Release

Once your PR is approved and merged to main, the release pipeline automatically:
- Determines the new version based on commit messages
- Generates comprehensive release notes
- Builds and tests the package
- Publishes to GitHub Packages
- Creates a GitHub Release with assets

## 🔄 Pipeline Workflows

### Release Pipeline (`release.yml`)
**Triggers**: Push to main/master branch

**Process**:
1. **Test** - Runs comprehensive test suite across multiple Node.js versions
2. **Build** - Creates package with TypeScript declarations
3. **Version** - Determines semantic version based on commits
4. **Release Notes** - Generates categorized changelog
5. **Publish** - Deploys to GitHub Packages
6. **GitHub Release** - Creates tagged release with assets

### PR Check Pipeline (`pr-check.yml`)
**Triggers**: Pull requests to main/master

**Validation**:
- ✅ ESLint compliance (zero errors)
- ✅ Prettier formatting
- ✅ Unit tests (all Node.js versions)
- ✅ Build verification with TypeScript declarations
- ✅ Security audit (no high/critical vulnerabilities)
- ✅ Automated PR comments with results

### Dependency Update Pipeline (`dependency-update.yml`)
**Triggers**: Weekly schedule (Mondays 9 AM UTC) + manual dispatch

**Features**:
- 🔍 Scans for outdated dependencies
- 🛡️ Identifies security vulnerabilities
- 🔄 Creates automated update PRs
- ✅ Validates updates don't break functionality

## 📝 Conventional Commits

The pipeline uses [Conventional Commits](https://www.conventionalcommits.org/) for automatic versioning:

### Commit Types and Version Bumps

| Commit Type | Version Bump | Example |
|-------------|--------------|---------|
| `feat:` | **Minor** (0.1.0 → 0.2.0) | `feat: add subscription billing` |
| `fix:` | **Patch** (0.1.0 → 0.1.1) | `fix: resolve validation error` |
| `perf:` | **Patch** (0.1.0 → 0.1.1) | `perf: optimize payment processing` |
| `BREAKING CHANGE:` | **Major** (0.1.0 → 1.0.0) | `feat!: redesign API` |
| `docs:` | **No bump** | `docs: update README` |
| `style:` | **No bump** | `style: fix formatting` |
| `test:` | **No bump** | `test: add payment tests` |
| `chore:` | **No bump** | `chore: update dependencies` |

### Commit Message Format

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

### Examples

```bash
# Patch release (0.1.0 → 0.1.1)
git commit -m "fix: resolve payment validation issue"

# Minor release (0.1.0 → 0.2.0)
git commit -m "feat: add Apple Pay support"

# Major release (0.1.0 → 1.0.0)
git commit -m "feat!: redesign API interface

BREAKING CHANGE: Constructor parameters have changed"

# No version bump
git commit -m "docs: update installation instructions"
git commit -m "test: add unit tests for payment methods"
git commit -m "chore: update ESLint configuration"
```

## 📊 Release Notes

Release notes are automatically generated with the following sections:

- 🚀 **Features** - New functionality
- 🐛 **Bug Fixes** - Fixed issues
- ⚡ **Performance Improvements** - Optimizations
- 📚 **Documentation** - Documentation updates
- 🛠 **Build System** - Build-related changes
- ⚙️ **CI** - CI/CD improvements
- 📦 **Code Refactoring** - Code improvements
- 🚨 **Tests** - Test additions/improvements
- **Contributors** - List of contributors to the release

## 🔐 Security & Authentication

### GitHub Secrets Required
- `GITHUB_TOKEN` - Automatically provided by GitHub Actions

### Permissions Required
- `contents: write` - Creating releases and tags
- `packages: write` - Publishing to GitHub Packages
- `pull-requests: write` - PR commenting
- `issues: write` - Issue management

## 📁 Project Structure

```
.
├── .github/
│   └── workflows/
│       ├── release.yml              # Main release pipeline
│       ├── pr-check.yml             # PR validation workflow
│       └── dependency-update.yml    # Dependency management
├── packages/@sparrow-billing/stripe/
│   ├── .releaserc.json             # Semantic-release configuration
│   ├── CI_CD_GUIDE.md              # Detailed usage guide
│   ├── package.json                # Updated with CI/CD scripts
│   └── src/                        # Package source code
├── scripts/
│   └── setup-cicd.js               # Setup validation script
├── CI_CD_IMPLEMENTATION_SUMMARY.md # Implementation overview
└── README.md                       # This file
```

## 🛠 Configuration Files

### `.releaserc.json`
Configures semantic-release behavior:
- Commit analysis rules
- Release notes generation
- Plugin configuration
- Branch and tag settings

### `package.json` Scripts
Added CI/CD-specific scripts:
- `lint:check` - Linting without auto-fix
- `format:check` - Format checking without auto-fix
- `release` - Manual semantic-release trigger
- `release:dry` - Dry-run semantic-release

## 📚 Documentation

- **[CI_CD_GUIDE.md](packages/@sparrow-billing/stripe/CI_CD_GUIDE.md)** - Comprehensive usage guide
- **[CI_CD_IMPLEMENTATION_SUMMARY.md](CI_CD_IMPLEMENTATION_SUMMARY.md)** - Implementation details
- **Inline comments** in all workflow files for easy understanding

## 🔧 Troubleshooting

### Common Issues

#### Pipeline Fails on Tests
```bash
# Run tests locally
cd packages/@sparrow-billing/stripe
npm test
npm run test:cov
```

#### Build Failures
```bash
# Check build locally
npm run build
# Verify TypeScript declarations
ls -la dist/*.d.ts
```

#### Version Not Bumped
- Check commit message format
- Verify conventional commit compliance
- Review semantic-release logs in Actions

#### Dependency Issues
```bash
# Fix npm dependency and lock file issues
node scripts/fix-dependencies.js
```

### Getting Help
1. Check GitHub Actions logs for detailed error messages
2. Review the CI_CD_GUIDE.md for configuration details
3. Validate setup using `node scripts/setup-cicd.js`
4. Fix dependency issues using `node scripts/fix-dependencies.js`

## 🎯 Best Practices

1. **Write Clear Commit Messages** - Follow conventional commits format
2. **Test Locally First** - Run `npm test` and `npm run build` before pushing
3. **Keep PRs Small** - Easier to review and less likely to break
4. **Monitor Pipeline Status** - Check Actions tab regularly
5. **Review Release Notes** - Ensure generated notes are accurate

## 📈 Monitoring

### Key Metrics
- ✅ PR success rate
- 🚀 Release frequency
- 🐛 Failed pipeline rate
- 📦 Package download statistics

### Pipeline Status
Monitor pipeline health through:
- GitHub Actions dashboard
- Workflow status badges
- Automated PR comments
- Release notifications

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch with conventional naming
3. Make changes following the coding standards
4. Write tests for new functionality
5. Use conventional commit messages
6. Create a pull request

The CI/CD pipeline will automatically validate your changes and provide feedback.

## 📄 License

This CI/CD pipeline implementation is part of the sparrow-packages repository.
See the main repository for license information.

---

**🎉 Happy Coding!** The CI/CD pipeline will handle the rest! 🚀
