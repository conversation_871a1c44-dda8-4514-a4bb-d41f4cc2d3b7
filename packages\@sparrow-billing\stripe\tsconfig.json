{"compilerOptions": {"typeRoots": ["./node_modules/@types", "./src/types"], "module": "commonjs", "declaration": true, "declarationMap": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2021", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strictNullChecks": true, "forceConsistentCasingInFileNames": true, "noImplicitAny": false, "strictBindCallApply": false, "noFallthroughCasesInSwitch": false}, "include": ["src/**/*.ts", "src/custom.d.ts"], "exclude": ["node_modules", "dist", "test"]}