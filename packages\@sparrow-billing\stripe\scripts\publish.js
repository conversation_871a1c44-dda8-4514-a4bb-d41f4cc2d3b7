#!/usr/bin/env node
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// The GitHub token needs to be passed as an environment variable or use the default
const token = process.env.GITHUB_TOKEN;

// Create a temporary .npmrc file in the package directory
const npmrcPath = path.resolve(process.cwd(), '.npmrc');
const npmrcContent = `# GitHub Packages configuration
@sparrowapp-dev:registry=https://npm.pkg.github.com/
//npm.pkg.github.com/:_authToken=${token}

# Default registry for other packages
registry=https://registry.npmjs.org/
`;

try {
  // Write the .npmrc file
  fs.writeFileSync(npmrcPath, npmrcContent, { encoding: 'utf8' });
  console.log('Created temporary .npmrc file');

  // Clean the dist directory
  console.log('Cleaning dist directory...');
  execSync('npm run prebuild', { stdio: 'inherit' });

  // Build the package with proper declaration files
  console.log('Building package with TypeScript declarations...');
  execSync('tsc -p tsconfig.json', { stdio: 'inherit' });

  // Run the NestJS build to ensure proper module structure
  console.log('Running NestJS build...');
  execSync('nest build', { stdio: 'inherit' });

  // Verify declaration files were generated
  const declarationFiles = fs
    .readdirSync('./dist')
    .filter((file) => file.endsWith('.d.ts'));
  console.log(
    `Generated ${declarationFiles.length} TypeScript declaration files`,
  );

  if (declarationFiles.length === 0) {
    throw new Error(
      'No TypeScript declaration files were generated. Check your tsconfig.json',
    );
  }

  // Publish the package
  console.log('Publishing package...');
  execSync('npm publish', { stdio: 'inherit' });

  console.log('Package published successfully!');
} catch (error) {
  console.error(`Error: ${error.message}`);
  process.exit(1);
} finally {
  // Clean up the temporary .npmrc file
  if (fs.existsSync(npmrcPath)) {
    fs.unlinkSync(npmrcPath);
    console.log('Removed temporary .npmrc file');
  }
}
