import { DynamicModule, Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { StripeService } from './services/stripe.service';
import { StripeController } from './controllers/stripe.controller';
import { PaymentMethodsController } from './controllers/payment-methods.controller';
import { PaymentMethodsService } from './services/payment-methods.service';

export interface StripeModuleOptions {
  isGlobal?: boolean;
  secretKey?: string;
  publishableKey?: string;
  webhookSecret?: string;
  registerControllers?: boolean;
}

@Module({})
export class StripeModule {
  static register(options: StripeModuleOptions = {}): DynamicModule {
    const providers = [StripeService, PaymentMethodsService];
    const controllers =
      options.registerControllers !== false
        ? [StripeController, PaymentMethodsController]
        : [];

    return {
      global: options.isGlobal || false,
      module: StripeModule,
      imports: [
        ConfigModule.forRoot({
          isGlobal: true,
        }),
      ],
      controllers,
      providers: [
        ...providers,
        {
          provide: 'STRIPE_OPTIONS',
          useValue: {
            secretKey: options.secretKey,
            publishableKey: options.publishableKey,
            webhookSecret: options.webhookSecret,
          },
        },
      ],
      exports: providers,
    };
  }
}
