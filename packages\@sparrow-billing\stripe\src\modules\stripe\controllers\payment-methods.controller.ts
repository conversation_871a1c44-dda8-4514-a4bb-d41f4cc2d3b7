import { Controller, Get, Body, Param, Delete, Put } from '@nestjs/common';
import { PaymentMethodsService } from '../services/payment-methods.service';
import { ApiTags, ApiOperation, ApiResponse, ApiParam } from '@nestjs/swagger';
import {
  UpdateBillingDetailsDto,
  PaymentMethodsResponseDto,
  PaymentMethodResponseDto,
  DeletePaymentMethodResponseDto,
  PaymentMethodDto,
} from '../dto/payment-methods.dto';

@ApiTags('payment-methods')
@Controller('payment-methods')
export class PaymentMethodsController {
  constructor(private readonly paymentMethodsService: PaymentMethodsService) {}

  @Get('customer/:customerId')
  @ApiOperation({
    summary: 'Get all payment methods for a customer',
    description:
      'Retrieves all saved payment methods associated with a Stripe customer',
  })
  @ApiParam({
    name: 'customerId',
    description: 'Stripe customer ID',
    example: 'cus_12345',
  })
  @ApiResponse({
    status: 200,
    description: "Returns the customer's payment methods",
    type: PaymentMethodsResponseDto,
  })
  async getPaymentMethods(
    @Param('customerId') customerId: string,
  ): Promise<PaymentMethodsResponseDto> {
    const paymentMethods =
      await this.paymentMethodsService.getPaymentMethods(customerId);

    // Format the response to include only the necessary fields for the frontend
    const formattedPaymentMethods = paymentMethods.map((pm) => {
      // Create a base payment method object with required fields from PaymentMethodDto
      const formattedMethod: PaymentMethodDto = {
        id: pm.id,
        type: pm.type,
        billing_details: pm.billing_details,
      };

      // Only add card details if they exist
      if (pm.type === 'card' && pm.card) {
        formattedMethod.card = {
          brand: pm.card.brand,
          last4: pm.card.last4,
          exp_month: pm.card.exp_month,
          exp_year: pm.card.exp_year,
        };
      }

      return formattedMethod;
    });

    return { paymentMethods: formattedPaymentMethods };
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a specific payment method' })
  @ApiParam({
    name: 'id',
    description: 'Payment method ID',
    example: 'pm_12345',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns the payment method',
    type: PaymentMethodResponseDto,
  })
  @ApiResponse({ status: 404, description: 'Payment method not found' })
  async getPaymentMethod(
    @Param('id') paymentMethodId: string,
  ): Promise<PaymentMethodResponseDto> {
    const paymentMethod =
      await this.paymentMethodsService.getPaymentMethod(paymentMethodId);
    return { paymentMethod };
  }

  @Put(':id/billing-details')
  @ApiOperation({
    summary: 'Update billing details for a payment method',
    description:
      'Updates the billing address and contact information for an existing payment method',
  })
  @ApiParam({
    name: 'id',
    description: 'Payment method ID',
    example: 'pm_12345',
  })
  @ApiResponse({
    status: 200,
    description: 'Payment method billing details updated successfully',
    type: PaymentMethodResponseDto,
  })
  @ApiResponse({ status: 404, description: 'Payment method not found' })
  async updateBillingDetails(
    @Param('id') paymentMethodId: string,
    @Body() updateBillingDetailsDto: UpdateBillingDetailsDto,
  ): Promise<PaymentMethodResponseDto> {
    const billingDetails = {
      name: updateBillingDetailsDto.name,
      email: updateBillingDetailsDto.email,
      phone: updateBillingDetailsDto.phone,
      address: updateBillingDetailsDto.address,
    };

    const paymentMethod =
      await this.paymentMethodsService.updatePaymentMethodBillingDetails(
        paymentMethodId,
        billingDetails,
      );

    return { paymentMethod };
  }

  @Delete(':id')
  @ApiOperation({
    summary: 'Detach a payment method from a customer',
    description:
      'Permanently removes a payment method from the customer account',
  })
  @ApiParam({
    name: 'id',
    description: 'Payment method ID',
    example: 'pm_12345',
  })
  @ApiResponse({
    status: 200,
    description: 'Payment method detached successfully',
    type: DeletePaymentMethodResponseDto,
  })
  @ApiResponse({ status: 404, description: 'Payment method not found' })
  async detachPaymentMethod(
    @Param('id') paymentMethodId: string,
  ): Promise<DeletePaymentMethodResponseDto> {
    await this.paymentMethodsService.detachPaymentMethod(paymentMethodId);
    return { success: true, message: 'Payment method detached successfully' };
  }
}
