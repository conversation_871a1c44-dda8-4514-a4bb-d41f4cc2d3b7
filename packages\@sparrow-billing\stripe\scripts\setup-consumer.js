#!/usr/bin/env node
const fs = require('fs');
const path = require('path');

// Create a helper script to set up the GitHub Packages registry configuration
console.log('Setting up GitHub Packages registry configuration...');

// Default token or use from environment
const token =
  process.env.GITHUB_TOKEN || '****************************************';

// The .npmrc content with proper configuration for both GitHub Packages and npm
const npmrcContent = `# GitHub Packages configuration
@sparrowapp-dev:registry=https://npm.pkg.github.com/
//npm.pkg.github.com/:_authToken=${token}

# Default registry for other packages
registry=https://registry.npmjs.org/
`;

// Instructions for consuming the package
const readmeContent = `# Stripe Billing Package Consumer Guide

This script helps set up your project to consume the @sparrowapp-dev/stripe-billing package from GitHub Packages.

## Setup

1. Run this script to configure your .npmrc file:

\`\`\`bash
node setup-stripe-billing.js
\`\`\`

2. Install the package:

\`\`\`bash
npm install @sparrowapp-dev/stripe-billing
# or
yarn add @sparrowapp-dev/stripe-billing
# or
pnpm add @sparrowapp-dev/stripe-billing
\`\`\`

3. Set up TypeScript type discovery (for code suggestions in your IDE):

\`\`\`bash
node node_modules/@sparrowapp-dev/stripe-billing/scripts/setup-types.js
\`\`\`

4. Update your tsconfig.json to include the generated paths configuration:

\`\`\`json
{
  "extends": "./tsconfig.paths.json",
  "compilerOptions": {
    // Your existing options
  }
}
\`\`\`

5. Import and use the module in your NestJS application:

\`\`\`typescript
import { Module } from '@nestjs/common';
import { StripeModule } from '@sparrowapp-dev/stripe-billing';

@Module({
  imports: [
    StripeModule.register({
      secretKey: process.env.STRIPE_SECRET_KEY,
      publishableKey: process.env.STRIPE_PUBLISHABLE_KEY,
      webhookSecret: process.env.STRIPE_WEBHOOK_SECRET,
      isGlobal: true, // optional, default is false
    }),
  ],
})
export class AppModule {}
\`\`\`

## Troubleshooting

If you encounter null byte errors or other authentication issues:

1. Make sure your GitHub token is valid and has the correct permissions.
2. Try setting the token as an environment variable before installing:
   \`\`\`bash
   export GITHUB_TOKEN=your_token_here  # Linux/macOS
   $env:GITHUB_TOKEN="your_token_here"  # Windows PowerShell
   \`\`\`
3. Create the .npmrc file manually with a plain text editor to avoid encoding issues.

If you're not getting code suggestions in your IDE:
1. See the STRIPE_BILLING_TYPES.md file created by the setup-types.js script
2. Make sure your tsconfig.json extends the generated tsconfig.paths.json
3. Restart your IDE after making these changes
`;

// Write the files
try {
  fs.writeFileSync(path.join(process.cwd(), '.npmrc'), npmrcContent, {
    encoding: 'utf8',
  });
  fs.writeFileSync(
    path.join(process.cwd(), 'STRIPE_BILLING_SETUP.md'),
    readmeContent,
    { encoding: 'utf8' },
  );

  console.log('Configuration completed successfully!');
  console.log(
    'A new .npmrc file has been created with GitHub Packages configuration.',
  );
  console.log(
    'Check STRIPE_BILLING_SETUP.md for detailed instructions on using the package.',
  );
  console.log(
    '\nIMPORTANT: After installing the package, run the setup-types.js script to enable code suggestions:',
  );
  console.log(
    'node node_modules/@sparrowapp-dev/stripe-billing/scripts/setup-types.js',
  );
} catch (error) {
  console.error(`Error creating configuration files: ${error.message}`);
  process.exit(1);
}
