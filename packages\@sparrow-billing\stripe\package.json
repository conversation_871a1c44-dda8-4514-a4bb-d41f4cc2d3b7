{"name": "@sparrowapp-dev/stripe-billing", "version": "0.0.9", "description": "Reusable Stripe billing module for NestJS applications", "author": "Sparrowapp-dev", "private": false, "license": "UNLICENSED", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.js"}}, "files": ["dist/**/*", "README.md", "scripts/setup-consumer.js", "scripts/setup-types.js"], "publishConfig": {"registry": "https://npm.pkg.github.com/", "access": "restricted"}, "repository": {"type": "git", "url": "git+https://github.com/sparrowapp-dev/sparrow-packages.git"}, "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "prepare": "npm run build", "prepublishOnly": "npm run lint && npm run test", "publish-package": "node scripts/publish.js", "setup-consumer": "node scripts/setup-consumer.js", "setup-types": "node scripts/setup-types.js", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "format:check": "prettier --check \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "lint:check": "eslint \"{src,apps,libs,test}/**/*.ts\"", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "version": "conventional-changelog -p angular -i CHANGELOG.md -s && git add CHANGELOG.md", "release": "semantic-release", "release:dry": "semantic-release --dry-run"}, "dependencies": {"@nestjs/common": "^10.0.0", "@nestjs/config": "^3.1.1", "@nestjs/core": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/swagger": "^7.1.17", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "stripe": "^14.17.0"}, "peerDependencies": {"@nestjs/common": "^10.0.0 || ^11.0.0", "@nestjs/core": "^10.0.0 || ^11.0.0"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@semantic-release/changelog": "^6.0.3", "@semantic-release/commit-analyzer": "^11.1.0", "@semantic-release/git": "^10.0.1", "@semantic-release/github": "^9.2.6", "@semantic-release/npm": "^11.0.2", "@semantic-release/release-notes-generator": "^12.1.0", "@types/express": "^4.17.21", "@types/jest": "^29.5.5", "@types/node": "^20.8.2", "@typescript-eslint/eslint-plugin": "^6.7.5", "@typescript-eslint/parser": "^6.7.5", "conventional-changelog-cli": "^4.1.0", "conventional-changelog-conventionalcommits": "^7.0.2", "eslint": "^8.51.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.7.0", "prettier": "^3.0.3", "rimraf": "^5.0.1", "semantic-release": "^22.0.12", "source-map-support": "^0.5.21", "ts-jest": "^29.1.1", "ts-loader": "^9.4.4", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.2.2"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}