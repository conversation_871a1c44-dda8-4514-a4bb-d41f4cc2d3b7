import { Test, TestingModule } from '@nestjs/testing';
import { ConfigModule } from '@nestjs/config';
import { StripeModule, StripeModuleOptions } from './stripe.module';
import { StripeService } from './services/stripe.service';
import { PaymentMethodsService } from './services/payment-methods.service';
import { StripeController } from './controllers/stripe.controller';
import { PaymentMethodsController } from './controllers/payment-methods.controller';

describe('StripeModule', () => {
  describe('Module Registration', () => {
    it('should create module with default options', async () => {
      const module: TestingModule = await Test.createTestingModule({
        imports: [StripeModule.register()],
      }).compile();

      expect(module).toBeDefined();
      expect(module.get(StripeService)).toBeDefined();
      expect(module.get(PaymentMethodsService)).toBeDefined();
    });

    it('should create module with custom options', async () => {
      const options: StripeModuleOptions = {
        isGlobal: true,
        secretKey: 'sk_test_custom',
        publishableKey: 'pk_test_custom',
        webhookSecret: 'whsec_custom',
        registerControllers: true,
      };

      const module: TestingModule = await Test.createTestingModule({
        imports: [StripeModule.register(options)],
      }).compile();

      expect(module).toBeDefined();
      expect(module.get(StripeService)).toBeDefined();
      expect(module.get(PaymentMethodsService)).toBeDefined();
      expect(module.get('STRIPE_OPTIONS')).toEqual({
        secretKey: options.secretKey,
        publishableKey: options.publishableKey,
        webhookSecret: options.webhookSecret,
      });
    });

    it('should register controllers by default', async () => {
      const module: TestingModule = await Test.createTestingModule({
        imports: [StripeModule.register()],
      }).compile();

      expect(module).toBeDefined();
      // Controllers should be registered by default
      expect(() => module.get(StripeController)).not.toThrow();
      expect(() => module.get(PaymentMethodsController)).not.toThrow();
    });

    it('should not register controllers when disabled', async () => {
      const options: StripeModuleOptions = {
        registerControllers: false,
      };

      const module: TestingModule = await Test.createTestingModule({
        imports: [StripeModule.register(options)],
      }).compile();

      expect(module).toBeDefined();
      expect(module.get(StripeService)).toBeDefined();
      expect(module.get(PaymentMethodsService)).toBeDefined();
    });

    it('should create global module when specified', async () => {
      const options: StripeModuleOptions = {
        isGlobal: true,
      };

      const moduleDefinition = StripeModule.register(options);
      expect(moduleDefinition.global).toBe(true);
    });

    it('should create non-global module by default', async () => {
      const moduleDefinition = StripeModule.register();
      expect(moduleDefinition.global).toBe(false);
    });
  });

  describe('Module Configuration', () => {
    it('should include ConfigModule in imports', async () => {
      const moduleDefinition = StripeModule.register();
      expect(moduleDefinition.imports).toContain(
        expect.objectContaining({
          module: ConfigModule,
        })
      );
    });

    it('should export services', async () => {
      const moduleDefinition = StripeModule.register();
      expect(moduleDefinition.exports).toContain(StripeService);
      expect(moduleDefinition.exports).toContain(PaymentMethodsService);
    });

    it('should provide STRIPE_OPTIONS token', async () => {
      const options: StripeModuleOptions = {
        secretKey: 'test_secret',
        publishableKey: 'test_publishable',
        webhookSecret: 'test_webhook',
      };

      const moduleDefinition = StripeModule.register(options);
      const stripeOptionsProvider = moduleDefinition.providers?.find(
        (provider: any) => provider.provide === 'STRIPE_OPTIONS'
      );

      expect(stripeOptionsProvider).toBeDefined();
      expect(stripeOptionsProvider?.useValue).toEqual({
        secretKey: options.secretKey,
        publishableKey: options.publishableKey,
        webhookSecret: options.webhookSecret,
      });
    });
  });

  describe('Module Integration', () => {
    it('should work with NestJS dependency injection', async () => {
      const module: TestingModule = await Test.createTestingModule({
        imports: [StripeModule.register()],
      }).compile();

      const stripeService = module.get(StripeService);
      const paymentMethodsService = module.get(PaymentMethodsService);

      expect(stripeService).toBeInstanceOf(StripeService);
      expect(paymentMethodsService).toBeInstanceOf(PaymentMethodsService);
    });

    it('should handle empty options object', async () => {
      const module: TestingModule = await Test.createTestingModule({
        imports: [StripeModule.register({})],
      }).compile();

      expect(module).toBeDefined();
      expect(module.get(StripeService)).toBeDefined();
      expect(module.get(PaymentMethodsService)).toBeDefined();
    });
  });

  describe('Type Safety', () => {
    it('should accept valid StripeModuleOptions', () => {
      const validOptions: StripeModuleOptions = {
        isGlobal: true,
        secretKey: 'sk_test_123',
        publishableKey: 'pk_test_123',
        webhookSecret: 'whsec_123',
        registerControllers: true,
      };

      expect(() => StripeModule.register(validOptions)).not.toThrow();
    });

    it('should work with partial options', () => {
      const partialOptions: StripeModuleOptions = {
        isGlobal: true,
      };

      expect(() => StripeModule.register(partialOptions)).not.toThrow();
    });
  });
});
