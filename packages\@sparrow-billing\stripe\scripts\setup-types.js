#!/usr/bin/env node
const fs = require('fs');
const path = require('path');

// Create a helper script to set up TypeScript path aliases for better IDE support
console.log('Setting up TypeScript path aliases for better IDE support...');

// Create a tsconfig.paths.json file
const tsconfigPathsContent = {
  compilerOptions: {
    paths: {
      '@sparrowapp-dev/stripe-billing': [
        './node_modules/@sparrowapp-dev/stripe-billing/dist',
      ],
      '@sparrowapp-dev/stripe-billing/*': [
        './node_modules/@sparrowapp-dev/stripe-billing/dist/*',
      ],
    },
  },
};

// Instructions for setting up TypeScript support
const readmeContent = `# TypeScript Support for Stripe Billing Package

This script helps set up TypeScript path aliases for better IDE support when using the @sparrowapp-dev/stripe-billing package.

## Setup

1. A \`tsconfig.paths.json\` file has been created in your project directory.

2. Update your main \`tsconfig.json\` to extend this paths configuration:

\`\`\`json
{
  "extends": "./tsconfig.paths.json",
  "compilerOptions": {
    // Your existing options
  }
}
\`\`\`

3. If you're using VS Code, reload your window to refresh the TypeScript language server.

4. For better type inference, you may need to add a reference to the types in your project:

\`\`\`typescript
// src/stripe-types.d.ts
/// <reference path="../node_modules/@sparrowapp-dev/stripe-billing/dist/index.d.ts" />
\`\`\`

## Importing from the package

After setting this up, you should get proper code suggestions when importing:

\`\`\`typescript
import { StripeModule, StripeService } from '@sparrowapp-dev/stripe-billing';
\`\`\`

## Troubleshooting

If you're still not getting suggestions:

1. Try restarting your IDE
2. Check that the types are actually present at \`node_modules/@sparrowapp-dev/stripe-billing/dist/index.d.ts\`
3. Make sure your tsconfig.json is properly configured for the package
4. Try using an explicit import path: \`import { StripeModule } from '@sparrowapp-dev/stripe-billing/dist/modules/stripe/stripe.module'\`
\`\`\`
`;

// Write the files
try {
  fs.writeFileSync(
    path.join(process.cwd(), 'tsconfig.paths.json'),
    JSON.stringify(tsconfigPathsContent, null, 2),
    { encoding: 'utf8' },
  );
  fs.writeFileSync(
    path.join(process.cwd(), 'STRIPE_BILLING_TYPES.md'),
    readmeContent,
    { encoding: 'utf8' },
  );

  console.log('Configuration completed successfully!');
  console.log(
    'A new tsconfig.paths.json file has been created to help with TypeScript path resolution.',
  );
  console.log(
    'Check STRIPE_BILLING_TYPES.md for detailed instructions on setting up TypeScript support.',
  );
} catch (error) {
  console.error(`Error creating configuration files: ${error.message}`);
  process.exit(1);
}
