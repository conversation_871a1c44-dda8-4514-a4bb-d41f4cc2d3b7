import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsEmail,
  IsString,
  IsOptional,
  IsObject,
  IsBoolean,
  IsNotEmpty,
} from 'class-validator';

export class CreateCustomerDto {
  @ApiProperty({
    description: 'Customer email address',
    example: '<EMAIL>',
  })
  @IsEmail()
  email: string;

  @ApiPropertyOptional({
    description: 'Additional metadata for the customer',
    example: { userId: '12345', companyName: 'Acme Inc.' },
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, string>;
}

export class CustomerResponseDto {
  @ApiProperty({
    description: 'The Stripe customer object',
    example: {
      id: 'cus_12345',
      email: '<EMAIL>',
      name: null,
      phone: null,
      created: 1622148000,
      metadata: { userId: '12345' },
    },
  })
  customer: Record<string, any>;
}

export class SetupIntentDto {
  @ApiProperty({
    description: 'The Stripe customer ID',
    example: 'cus_12345',
  })
  @IsString()
  customerId: string;
}

export class SetupIntentResponseDto {
  @ApiProperty({
    description: 'Client secret used for Stripe Elements',
    example: 'seti_12345_secret_67890',
  })
  clientSecret: string | null;
}

export class PublicKeyResponseDto {
  @ApiProperty({
    description: 'Stripe publishable key',
    example: 'pk_test_51JHy...',
  })
  publishableKey: string;
}

// Subscription related DTOs

export class CreateSubscriptionDto {
  @ApiProperty({
    description: 'The Stripe customer ID',
    example: 'cus_12345',
  })
  @IsString()
  @IsNotEmpty()
  customerId: string;

  @ApiProperty({
    description: 'The Stripe price ID for the subscription plan',
    example: 'price_12345',
  })
  @IsString()
  @IsNotEmpty()
  priceId: string;

  @ApiProperty({
    description: 'The Stripe payment method ID',
    example: 'pm_12345',
  })
  @IsString()
  @IsNotEmpty()
  paymentMethodId: string;

  @ApiPropertyOptional({
    description: 'Additional metadata for the subscription',
    example: { orderId: '12345', planName: 'Premium' },
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, string>;
}

export class UpdateSubscriptionDto {
  @ApiProperty({
    description: 'The new Stripe price ID for the subscription plan',
    example: 'price_67890',
  })
  @IsString()
  @IsNotEmpty()
  priceId: string;

  @ApiPropertyOptional({
    description: 'Payment method ID to use for this subscription',
    example: 'pm_12345',
  })
  @IsOptional()
  @IsString()
  paymentMethodId?: string;

  @ApiPropertyOptional({
    description: 'Additional metadata for the subscription',
    example: { orderId: '12345', planName: 'Premium' },
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, string>;
}

export class CancelSubscriptionDto {
  @ApiPropertyOptional({
    description:
      'Whether to cancel the subscription immediately (true) or at period end (false)',
    example: false,
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  cancelImmediately?: boolean;
}

export class ReactivateSubscriptionDto {
  @ApiPropertyOptional({
    description: 'Additional metadata for the subscription',
    example: { reason: 'Customer changed mind' },
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, string>;
}

export class WebhookEventDto {
  @ApiProperty({
    description: 'The raw Stripe webhook event payload',
  })
  @IsObject()
  payload: any;

  @ApiProperty({
    description: 'The Stripe signature from the request header',
    example: 'whsec_12345...',
  })
  @IsString()
  signature: string;
}

export class SubscriptionResponseDto {
  @ApiProperty({
    description: 'The Stripe subscription object',
    example: {
      id: 'sub_12345',
      status: 'active',
      current_period_end: 1654070400,
      current_period_start: 1651392000,
      items: { data: [{ price: { product: 'prod_12345' } }] },
    },
  })
  subscription: Record<string, any>;
}
export class SubscriptionsResponseDto {
  @ApiProperty({
    description: 'List of Stripe subscription objects',
    type: [Object],
  })
  subscriptions: Record<string, any>[];
}

export class PlansResponseDto {
  @ApiProperty({
    description: 'List of available subscription plans/prices',
    type: [Object],
  })
  plans: Record<string, any>[];
}

export class InvoicesResponseDto {
  @ApiProperty({
    description: 'List of customer invoices',
    type: [Object],
  })
  invoices: Record<string, any>[];
}

export class WebhookResponseDto {
  @ApiProperty({
    description: 'Status of the webhook processing',
    example: 'Webhook processed successfully',
  })
  status: string;
}
