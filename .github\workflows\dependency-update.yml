name: Dependency Updates

on:
  schedule:
    # Run every Monday at 9 AM UTC
    - cron: '0 9 * * 1'
  workflow_dispatch:
    inputs:
      update_type:
        description: 'Type of update to perform'
        required: false
        default: 'minor'
        type: choice
        options:
          - patch
          - minor
          - major

env:
  PACKAGE_PATH: packages/@sparrow-billing/stripe
  NODE_VERSION: '20'

jobs:
  check-dependencies:
    name: Check for Dependency Updates
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ${{ env.PACKAGE_PATH }}
    outputs:
      has-updates: ${{ steps.check-updates.outputs.has-updates }}
      update-summary: ${{ steps.check-updates.outputs.update-summary }}
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: ${{ env.PACKAGE_PATH }}/package-lock.json

      - name: Install dependencies
        run: npm ci

      - name: Check for outdated packages
        id: check-updates
        run: |
          # Install npm-check-updates globally
          npm install -g npm-check-updates
          
          # Check for updates
          ncu_output=$(ncu --format json --target ${{ github.event.inputs.update_type || 'minor' }}) || true
          
          if [ "$ncu_output" != "{}" ] && [ -n "$ncu_output" ]; then
            echo "has-updates=true" >> $GITHUB_OUTPUT
            echo "update-summary<<EOF" >> $GITHUB_OUTPUT
            echo "$ncu_output" >> $GITHUB_OUTPUT
            echo "EOF" >> $GITHUB_OUTPUT
            echo "📦 Found dependency updates available"
          else
            echo "has-updates=false" >> $GITHUB_OUTPUT
            echo "✅ All dependencies are up to date"
          fi

      - name: Security audit
        run: |
          audit_output=$(npm audit --audit-level=moderate --json) || true
          if echo "$audit_output" | jq -e '.vulnerabilities | length > 0' > /dev/null 2>&1; then
            echo "⚠️ Security vulnerabilities found:"
            npm audit --audit-level=moderate
          else
            echo "✅ No security vulnerabilities found"
          fi

  update-dependencies:
    name: Update Dependencies
    runs-on: ubuntu-latest
    needs: check-dependencies
    if: needs.check-dependencies.outputs.has-updates == 'true'
    defaults:
      run:
        working-directory: ${{ env.PACKAGE_PATH }}
    permissions:
      contents: write
      pull-requests: write
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: ${{ env.PACKAGE_PATH }}/package-lock.json

      - name: Configure Git
        run: |
          git config --global user.name "github-actions[bot]"
          git config --global user.email "github-actions[bot]@users.noreply.github.com"

      - name: Install dependencies
        run: npm ci

      - name: Install npm-check-updates
        run: npm install -g npm-check-updates

      - name: Update dependencies
        run: |
          # Update dependencies based on the specified level
          ncu -u --target ${{ github.event.inputs.update_type || 'minor' }}
          
          # Install updated dependencies
          npm install
          
          # Run tests to ensure updates don't break anything
          npm test

      - name: Run quality checks
        run: |
          # Run linting
          npm run lint
          
          # Run build to ensure everything still works
          npm run build
          
          # Verify TypeScript declarations are still generated
          if [ ! -d "dist" ] || [ -z "$(find dist -name '*.d.ts' -print -quit)" ]; then
            echo "❌ Build failed: TypeScript declarations not generated"
            exit 1
          fi

      - name: Create Pull Request
        uses: peter-evans/create-pull-request@v5
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          commit-message: |
            chore(deps): update dependencies
            
            - Updated dependencies to latest ${{ github.event.inputs.update_type || 'minor' }} versions
            - All tests passing
            - Build verification successful
          title: "chore(deps): Update dependencies (${{ github.event.inputs.update_type || 'minor' }})"
          body: |
            ## 📦 Dependency Updates
            
            This PR updates dependencies to their latest ${{ github.event.inputs.update_type || 'minor' }} versions.
            
            ### 🔍 Changes
            ${{ needs.check-dependencies.outputs.update-summary }}
            
            ### ✅ Verification
            - [x] All tests pass
            - [x] Build successful
            - [x] TypeScript declarations generated
            - [x] Linting passes
            
            ### 🤖 Automated Actions
            This PR was created automatically by the dependency update workflow.
            
            **Review checklist:**
            - [ ] Check for any breaking changes in updated packages
            - [ ] Verify all functionality still works as expected
            - [ ] Review any new security advisories
            
            ---
            *Created by GitHub Actions*
          branch: chore/dependency-updates-${{ github.run_number }}
          delete-branch: true
          draft: false
          labels: |
            dependencies
            automated
            chore

  security-updates:
    name: Security Updates
    runs-on: ubuntu-latest
    if: always()
    defaults:
      run:
        working-directory: ${{ env.PACKAGE_PATH }}
    permissions:
      contents: write
      pull-requests: write
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: ${{ env.PACKAGE_PATH }}/package-lock.json

      - name: Install dependencies
        run: npm ci

      - name: Check for security vulnerabilities
        id: security-check
        run: |
          audit_output=$(npm audit --audit-level=moderate --json) || true
          
          if echo "$audit_output" | jq -e '.vulnerabilities | length > 0' > /dev/null 2>&1; then
            echo "has-vulnerabilities=true" >> $GITHUB_OUTPUT
            echo "⚠️ Security vulnerabilities found"
            
            # Try to fix automatically
            npm audit fix --audit-level=moderate || true
            
            # Check if fixes were applied
            if [ -n "$(git status --porcelain)" ]; then
              echo "fixes-applied=true" >> $GITHUB_OUTPUT
            else
              echo "fixes-applied=false" >> $GITHUB_OUTPUT
            fi
          else
            echo "has-vulnerabilities=false" >> $GITHUB_OUTPUT
            echo "✅ No security vulnerabilities found"
          fi

      - name: Configure Git
        if: steps.security-check.outputs.fixes-applied == 'true'
        run: |
          git config --global user.name "github-actions[bot]"
          git config --global user.email "github-actions[bot]@users.noreply.github.com"

      - name: Run tests after security fixes
        if: steps.security-check.outputs.fixes-applied == 'true'
        run: |
          npm test
          npm run build

      - name: Create Security Fix PR
        if: steps.security-check.outputs.fixes-applied == 'true'
        uses: peter-evans/create-pull-request@v5
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          commit-message: |
            fix(security): apply automated security fixes
            
            - Applied npm audit fix for security vulnerabilities
            - All tests passing after fixes
          title: "fix(security): Apply automated security fixes"
          body: |
            ## 🔒 Security Fixes
            
            This PR applies automated security fixes for detected vulnerabilities.
            
            ### 🛡️ Security Updates
            Applied `npm audit fix` to resolve security issues.
            
            ### ✅ Verification
            - [x] All tests pass after fixes
            - [x] Build successful
            - [x] No breaking changes detected
            
            ### 🚨 Priority
            This is a security-related update and should be reviewed promptly.
            
            **Review checklist:**
            - [ ] Verify the security fixes don't introduce breaking changes
            - [ ] Check that all functionality still works
            - [ ] Review the specific vulnerabilities that were fixed
            
            ---
            *Created automatically by security update workflow*
          branch: fix/security-updates-${{ github.run_number }}
          delete-branch: true
          draft: false
          labels: |
            security
            automated
            priority-high

  notify-results:
    name: Notify Results
    runs-on: ubuntu-latest
    needs: [check-dependencies, update-dependencies, security-updates]
    if: always()
    
    steps:
      - name: Summary
        run: |
          echo "## 📊 Dependency Update Summary"
          
          if [ "${{ needs.check-dependencies.outputs.has-updates }}" == "true" ]; then
            echo "✅ Dependency updates were found and processed"
          else
            echo "ℹ️ No dependency updates available"
          fi
          
          if [ "${{ needs.update-dependencies.result }}" == "success" ]; then
            echo "✅ Dependency update PR created successfully"
          elif [ "${{ needs.update-dependencies.result }}" == "skipped" ]; then
            echo "⏭️ Dependency updates skipped (no updates available)"
          else
            echo "❌ Dependency update failed"
          fi
          
          if [ "${{ needs.security-updates.result }}" == "success" ]; then
            echo "✅ Security check completed"
          else
            echo "⚠️ Security check had issues"
          fi
