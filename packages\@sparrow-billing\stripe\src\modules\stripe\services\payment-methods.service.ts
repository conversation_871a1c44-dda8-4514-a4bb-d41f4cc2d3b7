import { Injectable, NotFoundException } from '@nestjs/common';
import { StripeService } from './stripe.service';
import Stripe from 'stripe';

@Injectable()
export class PaymentMethodsService {
  constructor(private stripeService: StripeService) {}

  async getPaymentMethods(customerId: string): Promise<Stripe.PaymentMethod[]> {
    const stripe = this.stripeService.getStripeInstance();
    const paymentMethods = await stripe.paymentMethods.list({
      customer: customerId,
      type: 'card',
    });

    return paymentMethods.data;
  }

  async getPaymentMethod(
    paymentMethodId: string,
  ): Promise<Stripe.PaymentMethod> {
    const stripe = this.stripeService.getStripeInstance();
    try {
      return await stripe.paymentMethods.retrieve(paymentMethodId);
    } catch {
      throw new NotFoundException('Payment method not found');
    }
  }

  async updatePaymentMethodBillingDetails(
    paymentMethodId: string,
    billingDetails: Stripe.PaymentMethodUpdateParams.BillingDetails,
  ): Promise<Stripe.PaymentMethod> {
    const stripe = this.stripeService.getStripeInstance();

    try {
      return await stripe.paymentMethods.update(paymentMethodId, {
        billing_details: billingDetails,
      });
    } catch {
      throw new NotFoundException(
        'Payment method not found or could not be updated',
      );
    }
  }

  async detachPaymentMethod(
    paymentMethodId: string,
  ): Promise<Stripe.PaymentMethod> {
    const stripe = this.stripeService.getStripeInstance();

    try {
      return await stripe.paymentMethods.detach(paymentMethodId);
    } catch {
      throw new NotFoundException(
        'Payment method not found or could not be detached',
      );
    }
  }
}
